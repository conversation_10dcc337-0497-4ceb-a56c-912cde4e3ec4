#!/bin/bash

# Script de test pour vérifier la correction du Path Traversal

echo "=== Test de sécurité - Path Traversal Fix ==="
echo

# Créer un répertoire de test
mkdir -p test_logs
mkdir -p messages

# Test 1: Configuration normale (doit fonctionner)
echo "Test 1: Configuration normale"
cat > test_normal.ini << EOF
server_ip = 127.0.0.1
server_port = 8080
ack_timeout = 10
max_ack_size = 1024
max_buffer_size = 4096
test_interval_hours = 1
health_check_enabled = 1
max_retry_attempts = 3
retry_delay_seconds = 5
messages_directory = messages
alert_log_file = test_logs/alerts.log
EOF

echo "Configuration normale créée dans test_normal.ini"
echo

# Test 2: Configuration avec tentative de Path Traversal (doit être bloquée)
echo "Test 2: Configuration avec Path Traversal"
cat > test_malicious.ini << EOF
server_ip = 127.0.0.1
server_port = 8080
ack_timeout = 10
max_ack_size = 1024
max_buffer_size = 4096
test_interval_hours = 1
health_check_enabled = 1
max_retry_attempts = 3
retry_delay_seconds = 5
messages_directory = ../../../etc
alert_log_file = ../../../tmp/malicious.log
EOF

echo "Configuration malicieuse créée dans test_malicious.ini"
echo

# Créer un message de test simple
echo "Test message" > messages/test.txt

echo "=== Instructions pour tester ==="
echo "1. Testez avec la configuration normale:"
echo "   ./server_tester test_normal.ini"
echo
echo "2. Testez avec la configuration malicieuse:"
echo "   ./server_tester test_malicious.ini"
echo
echo "3. Vérifiez les logs pour voir les warnings de sécurité"
echo
echo "La configuration malicieuse devrait afficher des warnings"
echo "et utiliser les valeurs par défaut au lieu des chemins dangereux."
