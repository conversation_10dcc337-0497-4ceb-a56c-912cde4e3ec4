# **STAGE 1: Build the C Program**
FROM registry.access.redhat.com/ubi9/ubi:latest as build

# Mettre à jour le système et installer les outils nécessaires
RUN echo "Updating system for automate tester build" && dnf update -y && \
    dnf install -y https://dl.fedoraproject.org/pub/epel/epel-release-latest-9.noarch.rpm && \
    dnf install -y dnf-plugins-core && \
    dnf clean all

# Activation des dépôts supplémentaires
RUN echo "Activating repository" && \
    dnf config-manager --set-enabled ubi-9-baseos-rpms ubi-9-appstream-rpms

# Installation des dépendances nécessaires pour la compilation
RUN echo "Installing build dependencies" && \
    dnf install -y \
        gcc \
        gcc-c++ \
        make \
        cmake \
        autoconf \
        automake \
        libtool \
        pkgconfig \
        glibc-devel \
        tar \
        gzip && \
    dnf clean all

# Vérifier l'installation de CMake
RUN cmake --version

# Création du workdir
WORKDIR /app

# Copier les fichiers source
COPY server_tester.c .
COPY CMakeLists.txt .
COPY server_tester.ini .
COPY messages/ ./messages/

# Compilation avec installation dans /opt/automate_tester
RUN echo "Starting compilation process" && \
    mkdir build && cd build && \
    echo "Build directory created, starting cmake" && \
    cmake -DCMAKE_INSTALL_PREFIX=/opt/automate_tester .. && \
    echo "CMAKE completed, starting make" && \
    make && \
    echo "Make completed, creating installation structure" && \
    mkdir -p /opt/automate_tester/bin && \
    mkdir -p /opt/automate_tester/etc && \
    mkdir -p /opt/automate_tester/var/logs && \
    mkdir -p /opt/automate_tester/var/data && \
    cp PPAEMD_Automates_simul /opt/automate_tester/bin/automate_tester && \
    cp ../server_tester.ini /opt/automate_tester/etc/config.ini && \
    cp -r ../messages /opt/automate_tester/var/data/ && \
    echo "Installation completed"

# **STAGE 2: Runtime Container**
FROM registry.access.redhat.com/ubi9/ubi-init

LABEL maintainer="Synlab IT Team" \
      description="Testeur Automatisé pour Driver d'Automate de Biologie Médicale" \
      version="1.0" \
      architecture="multi-arch" \
      target="iMX8 mini"

# Installation des dépendances runtime uniquement
RUN echo "Updating system for runtime" && dnf update -y && \
    dnf install -y https://dl.fedoraproject.org/pub/epel/epel-release-latest-9.noarch.rpm && \
    dnf install -y glibc procps-ng && \
    dnf clean all

# Copier les fichiers installés depuis le stage de build
COPY --from=build /opt/automate_tester/ /opt/automate_tester/

# Créer les répertoires de données avec les bonnes permissions
# Note: Les données seront montées depuis l'host via volume
RUN mkdir -p /opt/automate_tester/var/data && \
    mkdir -p /opt/automate_tester/var/logs && \
    chmod 755 /opt/automate_tester/bin/automate_tester && \
    chmod +x /opt/automate_tester/bin/automate_tester && \
    chmod 755 /opt/automate_tester/var/data && \
    chmod 755 /opt/automate_tester/var/logs && \
    chmod 644 /opt/automate_tester/etc/config.ini

# Variables d'environnement
ENV TZ=Europe/Paris
ENV PATH="/opt/automate_tester/bin:${PATH}"

# Point d'entrée du conteneur
ENTRYPOINT ["/opt/automate_tester/bin/automate_tester", "--daemon", "/opt/automate_tester/etc/config.ini"]

# Métadonnées pour les volumes
LABEL ports.none="No ports exposed" \
      volumes.data="/opt/automate_tester/var/data" \
      volumes.logs="/opt/automate_tester/var/logs" \
      usage="podman run -d --name automate_tester_service -v /opt/shared-data:/opt/automate_tester/var/data:Z -v /var/log/automate_tester/:/opt/automate_tester/var/logs:Z automate_tester:latest"

# Documentation des volumes attendus
VOLUME ["/opt/automate_tester/var/data", "/opt/automate_tester/var/logs"]

# Health check pour Podman
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD pgrep -f automate_tester || exit 1
