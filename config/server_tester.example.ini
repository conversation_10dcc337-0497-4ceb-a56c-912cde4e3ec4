# Configuration d'exemple pour le testeur automatisé
# Copiez ce fichier vers server_tester.ini et adaptez les paramètres

[network]
# Adresse IP du driver d'automate de biologie médicale
server_ip = *************

# Port TCP du driver
server_port = 50901

[timeouts]
# Timeout pour l'accusé de réception (en secondes)
ack_timeout = 10

[buffers]
# Taille maximale du buffer pour l'accusé de réception (en octets)
max_ack_size = 2048

# Taille maximale du buffer pour le fichier (en octets)
max_buffer_size = 131072

[monitoring]
# Intervalle entre les tests périodiques (en heures)
# Production: 2h, Test: 0.5h, Développement: 0.1h
test_interval_hours = 2

# Activer les vérifications de santé (1 = activé, 0 = désactivé)
health_check_enabled = 1

# Nombre maximum de tentatives en cas d'échec
max_retry_attempts = 5

# <PERSON><PERSON><PERSON> entre les tentatives (en secondes)
retry_delay_seconds = 10

# Répertoire contenant les messages de test (chemin dans le conteneur)
messages_directory = /opt/automate_tester/var/data/messages

# Fichier de log pour les alertes critiques (chemin dans le conteneur)
alert_log_file = /opt/automate_tester/var/logs/alerts.log

# Exemples de configuration par environnement :

# PRODUCTION CRITIQUE
# test_interval_hours = 1
# max_retry_attempts = 5
# retry_delay_seconds = 15

# PRODUCTION STANDARD  
# test_interval_hours = 2
# max_retry_attempts = 3
# retry_delay_seconds = 10

# TEST/QUALIFICATION
# test_interval_hours = 0.5
# max_retry_attempts = 2
# retry_delay_seconds = 5

# DÉVELOPPEMENT
# test_interval_hours = 0.1
# max_retry_attempts = 1
# retry_delay_seconds = 2
