# Configuration pour server_tester - Testeur automatisé pour driver d'automate de biologie médicale
# Paramètres de connexion réseau

[network]
# Adresse IP du conteneur EBMD
server_ip = *************

# Port TCP du conteneur EBMD
server_port = 50901

[timeouts]
# Timeout pour l'accusé de réception (en secondes)
ack_timeout = 5

[buffers]
# Taille maximale du buffer pour l'accusé de réception (en octets)
max_ack_size = 1024

# Taille maximale du buffer pour le fichier (en octets)
max_buffer_size = 65536

[monitoring]
# Intervalle entre les tests périodiques (en heures)
test_interval_hours = 1

# Activer les vérifications de santé (1 = activé, 0 = désactivé)
health_check_enabled = 1

# Nombre maximum de tentatives en cas d'échec
max_retry_attempts = 3

# Délai entre les tentatives (en secondes)
retry_delay_seconds = 5

# Répertoire contenant les messages de test
messages_directory = messages

# Fichier de log pour les alertes critiques
alert_log_file = logs/alerts.log