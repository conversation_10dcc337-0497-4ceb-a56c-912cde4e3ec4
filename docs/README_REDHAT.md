# Guide de Déploiement Red Hat Enterprise Linux

Ce guide détaille le déploiement du testeur automatisé d'automate de biologie médicale sur Red Hat Enterprise Linux avec <PERSON>dman, optimisé pour cartes iMX8 mini.

## 🎯 Spécificités Red Hat

### Architecture Cible
- **OS** : Red Hat Enterprise Linux 8/9
- **Conteneurisation** : <PERSON>dman (rootless containers)
- **Hardware** : Carte iMX8 mini (ARM64)
- **Sécurité** : SELinux activé
- **Monitoring** : systemd + journald

### Avantages de cette Configuration
- ✅ **Sécurité renforcée** : SELinux, conteneurs rootless
- ✅ **Stabilité enterprise** : Support Red Hat officiel
- ✅ **Monitoring intégré** : journald, systemd
- ✅ **Conformité** : Standards industriels
- ✅ **Performance** : Optimisé pour iMX8 mini

## 🚀 Installation Rapide

### Prérequis
```bash
# Vérifier la version Red Hat
cat /etc/redhat-release

# Vérifier l'architecture
uname -m  # doit afficher aarch64 pour iMX8 mini
```

### Installation Automatique
```bash
# Cloner le projet
git clone <repository>
cd automate_tester

# Installation complète (nécessite sudo)
sudo ./install_redhat.sh

# Éditer la configuration
sudo nano /opt/automate_tester/server_tester.ini

# Démarrer le service
sudo systemctl start automate-tester-podman
sudo systemctl enable automate-tester-podman
```

## 📋 Installation Manuelle Détaillée

### 1. Installation des Dépendances

```bash
# Mise à jour du système
sudo dnf update -y

# Installation d'EPEL
sudo dnf install -y https://dl.fedoraproject.org/pub/epel/epel-release-latest-9.noarch.rpm

# Installation de Podman et outils
sudo dnf install -y podman podman-compose buildah skopeo
sudo dnf install -y git curl wget tar gzip
sudo dnf install -y policycoreutils-python-utils selinux-policy-devel
```

### 2. Configuration SELinux

```bash
# Activer les booléens nécessaires
sudo setsebool -P container_manage_cgroup on
sudo setsebool -P virt_use_nfs on

# Créer les contextes pour les répertoires
sudo semanage fcontext -a -t container_file_t "/opt/automate_tester(/.*)?"
sudo semanage fcontext -a -t container_file_t "/var/log/automate_tester(/.*)?"
sudo semanage fcontext -a -t container_file_t "/opt/shared-data(/.*)?"
```

### 3. Création des Répertoires

```bash
# Créer la structure de répertoires
sudo mkdir -p /opt/automate_tester
sudo mkdir -p /var/log/automate_tester
sudo mkdir -p /opt/shared-data

# Appliquer les contextes SELinux
sudo restorecon -R /opt/automate_tester
sudo restorecon -R /var/log/automate_tester
sudo restorecon -R /opt/shared-data
```

### 4. Installation des Fichiers

```bash
# Copier les fichiers du projet
sudo cp -r * /opt/automate_tester/
sudo cp server_tester.redhat.ini /opt/automate_tester/server_tester.ini

# Rendre les scripts exécutables
sudo chmod +x /opt/automate_tester/start_podman.sh
```

### 5. Construction de l'Image

```bash
cd /opt/automate_tester
sudo podman build -t automate_tester:latest .
```

## 🔧 Gestion avec Podman

### Scripts de Gestion

```bash
# Script principal pour Podman
./start_podman.sh --help

# Configuration initiale du système
sudo ./start_podman.sh --setup

# Construire l'image
./start_podman.sh --build

# Lancer le conteneur
./start_podman.sh --run

# Voir les logs
./start_podman.sh --logs

# Arrêter le conteneur
./start_podman.sh --stop

# Statut du service
./start_podman.sh --status
```

### Commandes Podman Directes

```bash
# Lister les images
podman images

# Lister les conteneurs
podman ps -a

# Logs du conteneur
podman logs automate_tester_service

# Entrer dans le conteneur
podman exec -it automate_tester_service /bin/bash

# Statistiques de ressources
podman stats automate_tester_service
```

## 🛠️ Service systemd

### Gestion du Service

```bash
# Démarrer le service
sudo systemctl start automate-tester-podman

# Arrêter le service
sudo systemctl stop automate-tester-podman

# Redémarrer le service
sudo systemctl restart automate-tester-podman

# Statut du service
sudo systemctl status automate-tester-podman

# Activer au démarrage
sudo systemctl enable automate-tester-podman

# Logs du service
sudo journalctl -u automate-tester-podman -f
```

### Configuration du Service

Le service systemd est configuré pour :
- ✅ Démarrage automatique au boot
- ✅ Redémarrage automatique en cas d'échec
- ✅ Intégration avec journald
- ✅ Gestion des volumes Podman
- ✅ Sécurité renforcée

## 📊 Monitoring et Logs

### Logs systemd

```bash
# Logs en temps réel
sudo journalctl -u automate-tester-podman -f

# Logs depuis le démarrage
sudo journalctl -u automate-tester-podman --since today

# Logs avec priorité ERROR uniquement
sudo journalctl -u automate-tester-podman -p err
```

### Logs de l'Application

```bash
# Logs principaux
tail -f /var/log/automate_tester/server_tester_*.log

# Logs d'alertes
tail -f /var/log/automate_tester/alerts.log

# Rotation des logs
ls -la /var/log/automate_tester/
```

### Métriques de Performance

```bash
# Utilisation des ressources
podman stats automate_tester_service

# Espace disque
df -h /var/log/automate_tester
df -h /opt/shared-data

# Mémoire système
free -h

# Charge CPU
top -p $(pgrep -f automate_tester)
```

## 🔒 Sécurité

### Configuration SELinux

```bash
# Vérifier le statut SELinux
getenforce

# Voir les contextes des fichiers
ls -Z /opt/automate_tester/
ls -Z /var/log/automate_tester/

# Audit SELinux
sudo ausearch -m avc -ts recent
```

### Sécurité des Conteneurs

```bash
# Vérifier les capabilities
podman inspect automate_tester_service | grep -A 10 "CapAdd"

# Vérifier l'utilisateur
podman inspect automate_tester_service | grep -A 5 "User"

# Vérifier les volumes
podman inspect automate_tester_service | grep -A 10 "Mounts"
```

## 🚨 Dépannage

### Problèmes Courants

#### 1. Erreur de Permission SELinux
```bash
# Vérifier les logs SELinux
sudo ausearch -m avc -ts recent

# Réappliquer les contextes
sudo restorecon -R /opt/automate_tester
sudo restorecon -R /var/log/automate_tester
```

#### 2. Conteneur qui ne Démarre Pas
```bash
# Vérifier les logs Podman
podman logs automate_tester_service

# Vérifier la configuration
podman inspect automate_tester_service

# Tester manuellement
podman run -it --rm automate_tester:latest /bin/bash
```

#### 3. Problèmes de Réseau
```bash
# Vérifier la connectivité
ping 192.168.1.110

# Tester le port
telnet 192.168.1.110 50901

# Vérifier les règles firewall
sudo firewall-cmd --list-all
```

#### 4. Problèmes de Performance
```bash
# Vérifier les ressources
podman stats

# Vérifier l'espace disque
df -h

# Vérifier la mémoire
free -h

# Optimiser la configuration
nano /opt/automate_tester/server_tester.ini
```

## 📈 Optimisations iMX8 Mini

### Configuration Recommandée

```ini
# Dans server_tester.ini
[performance]
max_memory_usage = 48
max_cpu_usage = 40
max_log_size = 5
max_log_files = 10

[monitoring]
test_interval_hours = 2
health_check_enabled = 1
max_retry_attempts = 3
```

### Limites de Ressources Podman

```bash
# Limiter la mémoire
podman run --memory=64m automate_tester:latest

# Limiter le CPU
podman run --cpus=0.5 automate_tester:latest

# Limiter l'I/O
podman run --device-read-bps=/dev/sda:1mb automate_tester:latest
```

## 🔄 Maintenance

### Mise à Jour

```bash
# Mise à jour automatique
sudo ./install_redhat.sh --update

# Mise à jour manuelle
sudo systemctl stop automate-tester-podman
sudo ./start_podman.sh --build
sudo systemctl start automate-tester-podman
```

### Sauvegarde

```bash
# Sauvegarder la configuration
sudo cp /opt/automate_tester/server_tester.ini /backup/

# Sauvegarder les logs
sudo tar -czf /backup/logs_$(date +%Y%m%d).tar.gz /var/log/automate_tester/

# Exporter l'image
podman save automate_tester:latest | gzip > automate_tester_image.tar.gz
```

### Nettoyage

```bash
# Nettoyer les logs anciens
sudo find /var/log/automate_tester -name "*.log" -mtime +30 -delete

# Nettoyer les images Podman
podman image prune -f

# Nettoyer les conteneurs arrêtés
podman container prune -f
```

## 📞 Support

Pour toute question spécifique à Red Hat :
1. Consulter les logs : `journalctl -u automate-tester-podman`
2. Vérifier SELinux : `ausearch -m avc -ts recent`
3. Tester la connectivité réseau
4. Vérifier les ressources système
5. Contacter le support technique avec les logs
