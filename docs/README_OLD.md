# Automated Driver Tester

**Testeur automatisé pour drivers d'automates de biologie médicale**

Programme C professionnel pour tester et monitorer la connectivité des drivers de communication médicale (HL7, POCT) en continu. Optimisé pour déploiement sur Red Hat Enterprise Linux avec Podman.

## 🎯 Fonctionnalités

### ✅ Fonctionnalités Principales

1. **Envoi de trames périodiques** : Envoi automatique de trames HL7/POCT à intervalles configurables
2. **Analyse des réponses** : Analyse intelligente des accusés de réception (MSA, codes d'erreur)
3. **Monitoring continu** : Surveillance 24/7 de la disponibilité du driver
4. **Configuration flexible** : Paramètres ajustables via fichier INI
5. **Logging et alertes** : Logs structurés avec alertes automatiques
6. **Robustesse industrielle** : Retry logic, reconnexion automatique, gestion des signaux
7. **Conteneurisation Red Hat** : Podman + SELinux + systemd

### 🛡️ Sécurité et Robustesse

- **SELinux intégré** : Isolation renforcée avec contextes appropriés
- **Conteneurs rootless** : Exécution sécurisée sans privilèges root
- **Gestion des signaux** : Arrêt gracieux (SIGTERM) et rechargement config (SIGHUP)
- **Health checks** : Vérifications périodiques de connectivité
- **Monitoring statistiques** : Métriques temps réel et alertes intelligentes
- **Optimisations iMX8** : Limites mémoire/CPU adaptées aux ressources limitées

## 🏗️ Architecture

### Structure Refactorisée (Nouvelle)

Le code a été restructuré pour améliorer la maintenabilité :

```
automate_tester/
├── main.c                       # Point d'entrée principal (racine)
├── src/                         # Modules sources
│   ├── utils.c                 # Fonctions utilitaires génériques
│   ├── config.c                # Gestion de la configuration INI
│   ├── logging.c               # Système de logging complet
│   ├── network.c               # Fonctions réseau et tests
│   ├── monitoring.c            # Statistiques et monitoring
│   └── version.c               # Informations de version
├── include/                     # Headers (.h)
│   ├── server_tester.h         # Header principal
│   ├── common.h                # Définitions communes
│   └── ...
├── CMakeLists.txt              # Configuration CMake
├── Makefile                    # Makefile simple pour compilation rapide
├── REFACTORING_NOTES.md        # Documentation de la restructuration

### Avantages de la Restructuration

- **Maintenabilité** : Code organisé par fonctionnalité, modules réutilisables
- **Lisibilité** : Fichiers plus petits et focalisés (< 300 lignes chacun)
- **Extensibilité** : Facile d'ajouter de nouvelles fonctionnalités
- **Tests** : Possibilité de tests unitaires par module
- **Compilation** : Deux options (CMake et Makefile simple)

### Structure Ancienne (Dépréciée)
```
automate_tester/
├── server_tester.c              # Code source principal (1000+ lignes) - SUPPRIMÉ
├── server_tester.redhat.ini     # Configuration Red Hat optimisée
├── CMakeLists.txt              # Configuration de build
├── Dockerfile                  # Image multi-stage UBI9
├── start_podman.sh            # Gestion Podman complète
├── install_redhat.sh          # Installation automatisée Red Hat
├── test_suite.sh              # Suite de tests (18 tests)
├── messages/                  # Messages de test HL7/POCT
│   ├── m1.bin ... m8.bin
│   ├── m_query.bin
│   └── specifications/
├── logs/                      # Logs applicatifs
└── README_REDHAT.md          # Guide détaillé Red Hat
```

## 🚀 Installation Rapide (Red Hat)

### Prérequis
- Red Hat Enterprise Linux 8/9
- Carte iMX8 mini (architecture ARM64)
- Accès sudo

### Installation Automatique
```bash
# Cloner le projet
git clone <repository>
cd automate_tester

# Installation complète (une seule commande)
sudo ./install_redhat.sh

# Éditer la configuration
sudo nano /opt/automate_tester/server_tester.ini

# Démarrer le service
sudo systemctl start automate-tester-podman
sudo systemctl enable automate-tester-podman
```

## ⚙️ Configuration

Le fichier `server_tester.ini` contient tous les paramètres :

```ini
# Connexion réseau
server_ip = *************
server_port = 50901

# Monitoring
test_interval_hours = 2          # Tests toutes les 2h
health_check_enabled = 1         # Health checks activés
max_retry_attempts = 5           # 5 tentatives max
retry_delay_seconds = 10         # 10s entre tentatives

# Optimisations iMX8
max_memory_usage = 48            # 48MB max
max_cpu_usage = 40               # 40% CPU max
```

## 🔧 Utilisation

### Gestion du Service
```bash
# Statut du service
sudo systemctl status automate-tester-podman

# Logs en temps réel
sudo journalctl -u automate-tester-podman -f

# Redémarrage
sudo systemctl restart automate-tester-podman
```

### Gestion Podman Directe
```bash
# Construction de l'image
./start_podman.sh --build

# Lancement manuel
./start_podman.sh --run

# Voir les logs
./start_podman.sh --logs

# Statut détaillé
./start_podman.sh --status

# Arrêt
./start_podman.sh --stop
```

## 📊 Monitoring et Logs

### Logs Système (journald)
```bash
# Logs en temps réel
sudo journalctl -u automate-tester-podman -f

# Logs depuis aujourd'hui
sudo journalctl -u automate-tester-podman --since today

# Logs d'erreur uniquement
sudo journalctl -u automate-tester-podman -p err
```

### Logs Applicatifs
```bash
# Logs principaux
tail -f /var/log/automate_tester/server_tester_*.log

# Logs d'alertes
tail -f /var/log/automate_tester/alerts.log

# Statistiques en temps réel
./start_podman.sh --status
```

### Métriques de Performance
```bash
# Utilisation des ressources
podman stats automate_tester_service

# Espace disque
df -h /var/log/automate_tester

# Statut du conteneur
podman ps | grep automate_tester
```

## 🔍 Protocoles Supportés

### HL7 (Health Level 7)
- **Messages ORU** : Observation Result Unsolicited
- **Messages QRY** : Query/Requêtes
- **Accusés de réception MSA** : Message Acknowledgment (AA, AE, AR)
- **Analyse intelligente** : Détection automatique des codes de réponse

### POCT (Point of Care Testing)
- **Messages de résultats** : Données d'analyse
- **Messages de statut** : État des automates
- **Protocoles propriétaires** : Adaptable selon constructeur

### Analyse des Réponses
Le programme analyse automatiquement :
- ✅ **Réponses positives** : MSA|AA, OK, ACK
- ❌ **Réponses négatives** : MSA|AE, MSA|AR, ERROR, FAIL
- 📊 **Métriques** : Temps de réponse, taux de succès, pannes consécutives

## 🚨 Alertes Automatiques

### Seuils d'Alerte
- **3 échecs consécutifs** : Alerte critique
- **Timeout répétés** : Problème de connectivité
- **Perte de connectivité** : Driver potentiellement hors ligne
- **Taux d'échec > 20%** : Dégradation de service

### Notifications
- **Logs d'alertes** : `/var/log/automate_tester/alerts.log`
- **journald** : Intégration système Red Hat
- **Métriques** : Statistiques temps réel disponibles

## 🛠️ Dépannage

### Problèmes Courants

#### 1. Service ne démarre pas
```bash
# Vérifier le statut
sudo systemctl status automate-tester-podman

# Voir les erreurs
sudo journalctl -u automate-tester-podman --no-pager

# Vérifier SELinux
sudo ausearch -m avc -ts recent
```

#### 2. Connexion refusée au driver
```bash
# Tester la connectivité
ping *************
telnet ************* 50901

# Vérifier la configuration
cat /opt/automate_tester/server_tester.ini

# Vérifier le firewall
sudo firewall-cmd --list-all
```

#### 3. Problèmes de performance
```bash
# Vérifier les ressources
podman stats automate_tester_service

# Vérifier l'espace disque
df -h /var/log/automate_tester

# Optimiser la configuration
sudo nano /opt/automate_tester/server_tester.ini
```

### Tests et Validation
```bash
# Suite de tests complète
./test_suite.sh

# Test de compilation (CMake - recommandé)
mkdir build && cd build && cmake .. && make

# Test de compilation (Makefile simple)
make clean && make

# Test de connectivité manuelle
./build/PPAEMD_Automates_simul  # Avec CMake
# ou
./server_tester                 # Avec Makefile
```

## 🔒 Sécurité Red Hat

- **SELinux activé** : Isolation renforcée avec contextes appropriés
- **Conteneurs rootless** : Exécution sans privilèges root
- **Images UBI** : Images Red Hat certifiées et supportées
- **Validation stricte** : Entrées de configuration et buffers sécurisés
- **Audit trail** : Logs complets sans données sensibles

## 📈 Performance iMX8 Mini

**Optimisations spécifiques** :
- Mémoire limitée à 48MB (configurable)
- CPU limité à 40% d'un core
- Logs rotatifs automatiques (5MB max par fichier)
- Health checks optimisés (30s d'intervalle)
- Tests périodiques configurables (2h par défaut)

## 📚 Documentation

- **`README_REDHAT.md`** : Guide détaillé Red Hat/Podman
- **`REDHAT_ADAPTATIONS.md`** : Détail des adaptations techniques
- **`server_tester.redhat.ini`** : Configuration d'exemple optimisée

## 🆘 Support

**En cas de problème** :
1. Consulter les logs : `sudo journalctl -u automate-tester-podman -f`
2. Vérifier SELinux : `sudo ausearch -m avc -ts recent`
3. Tester la connectivité : `ping <IP_DRIVER>`
4. Valider la configuration : `./test_suite.sh`
5. Contacter le support avec les logs complets
