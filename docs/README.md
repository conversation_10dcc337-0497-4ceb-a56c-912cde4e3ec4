# Documentation - Automated Driver Tester

## 📚 Index de la Documentation

### 🏗️ Architecture et Conception
- **[ARCHITECTURE_v2.md](ARCHITECTURE_v2.md)** - Architecture technique détaillée v2.0
- **[PROJECT_STRUCTURE.md](PROJECT_STRUCTURE.md)** - Structure du projet et organisation
- **[REFACTORING_NOTES.md](REFACTORING_NOTES.md)** - Notes sur la refactorisation du code

### 🚀 Déploiement et Installation
- **[README_REDHAT.md](README_REDHAT.md)** - Guide spécifique Red Hat Enterprise Linux
- **[REDHAT_ADAPTATIONS.md](REDHAT_ADAPTATIONS.md)** - Adaptations techniques pour RHEL

### 🔒 Sécurité
- **[SECURITY_FIX_REPORT.md](SECURITY_FIX_REPORT.md)** - Rapport des correctifs de sécurité

### 🔮 Évolutions
- **[FUTURE_IMPROVEMENTS.md](FUTURE_IMPROVEMENTS.md)** - Améliorations futures planifiées

### 📋 Documentation Historique
- **[README_OLD.md](README_OLD.md)** - Ancien README (archive)

## 🎯 Guide de Navigation

### Pour les Développeurs
1. Commencer par **[../IMPLEMENTATION.md](../IMPLEMENTATION.md)** - Guide d'implémentation
2. Consulter **[ARCHITECTURE_v2.md](ARCHITECTURE_v2.md)** - Architecture technique
3. Voir **[REFACTORING_NOTES.md](REFACTORING_NOTES.md)** - Historique des changements

### Pour les Administrateurs Système
1. Lire **[README_REDHAT.md](README_REDHAT.md)** - Déploiement Red Hat
2. Consulter **[REDHAT_ADAPTATIONS.md](REDHAT_ADAPTATIONS.md)** - Optimisations RHEL

### Pour la Sécurité
1. Examiner **[SECURITY_FIX_REPORT.md](SECURITY_FIX_REPORT.md)** - Correctifs appliqués

### Pour la Planification
1. Consulter **[FUTURE_IMPROVEMENTS.md](FUTURE_IMPROVEMENTS.md)** - Roadmap

## 📖 Conventions de Documentation

- **Markdown** : Format standard pour tous les documents
- **Émojis** : Utilisés pour améliorer la lisibilité
- **Sections** : Structure cohérente avec titres hiérarchiques
- **Code** : Blocs de code avec syntaxe highlighting
- **Liens** : Références croisées entre documents

## 🔄 Mise à Jour

Cette documentation est maintenue en parallèle du code. Lors de modifications importantes :

1. Mettre à jour les documents concernés
2. Vérifier les liens croisés
3. Maintenir la cohérence des exemples
4. Archiver les anciennes versions si nécessaire

---

**Dernière mise à jour** : Septembre 2025  
**Version** : 2.0.0
