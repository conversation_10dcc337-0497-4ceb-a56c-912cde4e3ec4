# Professional Makefile for Automated Driver Tester
# For full CMake build (recommended), use: make cmake

# Project information
PROJECT_NAME = server_tester
VERSION = 2.0.0

# Compiler and flags
CC = gcc
CFLAGS = -Wall -Wextra -Wpedantic -std=c11 -Iinclude
CFLAGS_DEBUG = $(CFLAGS) -g -O0 -DDEBUG -DVERSION=\"$(VERSION)\"
CFLAGS_RELEASE = $(CFLAGS) -O3 -DNDEBUG -DVERSION=\"$(VERSION)\"

# Directories
SRCDIR = src
INCDIR = include
BUILDDIR = build
OBJDIR = $(BUILDDIR)/obj
BINDIR = $(BUILDDIR)/bin

# Source files
SOURCES = main.c \
          $(SRCDIR)/utils.c \
          $(SRCDIR)/config.c \
          $(SRCDIR)/logging.c \
          $(SRCDIR)/network.c \
          $(SRCDIR)/monitoring.c \
          $(SRCDIR)/version.c

# Object files
OBJECTS = $(OBJDIR)/main.o \
          $(SRCDIR)/utils.c:$(OBJDIR)/utils.o \
          $(SRCDIR)/config.c:$(OBJDIR)/config.o \
          $(SRCDIR)/logging.c:$(OBJDIR)/logging.o \
          $(SRCDIR)/network.c:$(OBJDIR)/network.o \
          $(SRCDIR)/monitoring.c:$(OBJDIR)/monitoring.o \
          $(SRCDIR)/version.c:$(OBJDIR)/version.o

# Convert source paths to object paths
OBJECTS := $(OBJDIR)/main.o \
           $(patsubst $(SRCDIR)/%.c,$(OBJDIR)/%.o,$(filter $(SRCDIR)/%.c,$(SOURCES)))

# Target executable
TARGET = $(BINDIR)/$(PROJECT_NAME)

.PHONY: all debug release clean cmake install test help

# Default target (release build)
all: release

# Debug build
debug: CFLAGS := $(CFLAGS_DEBUG)
debug: $(TARGET)

# Release build
release: CFLAGS := $(CFLAGS_RELEASE)
release: $(TARGET)

# Build executable
$(TARGET): $(OBJECTS) | $(BINDIR)
	$(CC) $(OBJECTS) -o $(TARGET)
	@echo "Built $(TARGET) successfully"

# Compile source files from src/
$(OBJDIR)/%.o: $(SRCDIR)/%.c | $(OBJDIR)
	$(CC) $(CFLAGS) -c $< -o $@

# Compile main.c from root
$(OBJDIR)/main.o: main.c | $(OBJDIR)
	$(CC) $(CFLAGS) -c $< -o $@

# Create directories
$(OBJDIR):
	mkdir -p $(OBJDIR)

$(BINDIR):
	mkdir -p $(BINDIR)

# Clean build files
clean:
	rm -rf $(BUILDDIR)
	@echo "Cleaned build directory"

# Use CMake build (recommended)
cmake:
	mkdir -p $(BUILDDIR)
	cd $(BUILDDIR) && cmake .. && make
	@echo "CMake build completed"

# Install (copy to /usr/local/bin)
install: cmake
	sudo cp $(BUILDDIR)/bin/PPAEMD_Automates_simul /usr/local/bin/server_tester
	sudo chmod +x /usr/local/bin/server_tester
	@echo "Installed to /usr/local/bin/server_tester"

# Run basic tests
test: release
	@echo "Running basic functionality test..."
	@timeout 3s $(TARGET) || echo "Test completed (timeout expected)"

# Show project information
info:
	@echo "Project: $(PROJECT_NAME)"
	@echo "Version: $(VERSION)"
	@echo "Sources: $(SOURCES)"
	@echo "Build directory: $(BUILDDIR)"

# Help
help:
	@echo "Available targets:"
	@echo "  all     - Build release version (default)"
	@echo "  debug   - Build debug version with symbols"
	@echo "  release - Build optimized release version"
	@echo "  cmake   - Build with CMake (recommended)"
	@echo "  clean   - Clean build files"
	@echo "  install - Install to /usr/local/bin"
	@echo "  test    - Run basic functionality test"
	@echo "  info    - Show project information"
	@echo "  help    - Show this help"
