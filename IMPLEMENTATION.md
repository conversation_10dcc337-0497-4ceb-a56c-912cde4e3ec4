# Guide d'Implémentation - Automated Driver Tester

## 🎯 Vue d'Ensemble

Ce document détaille l'implémentation technique du testeur automatisé pour drivers d'automates de biologie médicale. Il s'adresse aux développeurs qui doivent comprendre, maintenir ou étendre le code.

## 🏗️ Architecture Modulaire

### Structure des Fichiers

```
automate_tester/
├── main.c                  # Point d'entrée - orchestration générale
├── src/                    # Modules fonctionnels
│   ├── utils.c            # Utilitaires génériques et signaux
│   ├── config.c           # Gestion configuration INI
│   ├── logging.c          # Système de logging avec rotation
│   ├── network.c          # Communication réseau et tests
│   ├── monitoring.c       # Statistiques et alertes
│   └── version.c          # Informations de build
├── include/               # Interfaces publiques
│   ├── server_tester.h    # Header principal (inclut tout)
│   ├── common.h           # Structures et constantes communes
│   └── *.h               # Headers spécifiques par module
```

### Principe de Conception

- **Modularité** : Chaque module a une responsabilité claire
- **Encapsulation** : Interfaces publiques dans les headers
- **Simplicité** : Un seul `#include "server_tester.h"` suffit
- **Maintenabilité** : Modules < 300 lignes chacun

## 🔧 Modules Détaillés

### 1. main.c - Orchestrateur Principal

**Responsabilités :**
- Initialisation du système (logging, signaux, config)
- Boucle principale de test
- Gestion des modes daemon/single-shot
- Nettoyage des ressources

**Points Clés :**
```c
// Initialisation complète
init_logging(config.alert_log_path);
setup_signal_handlers();
load_config(&config, "server_tester.ini");

// Boucle principale
while (running) {
    perform_test_cycle(&config, messages, message_count);
    if (!config.daemon_mode) break;
    sleep(config.test_interval * 3600);
}
```

### 2. src/config.c - Gestion Configuration

**Responsabilités :**
- Lecture/parsing des fichiers INI
- Validation des paramètres
- Valeurs par défaut sécurisées

**Structure Config :**
```c
typedef struct {
    char server_ip[16];
    int server_port;
    int ack_timeout;
    int test_interval;
    bool daemon_mode;
    // ... autres paramètres
} Config;
```

### 3. src/network.c - Communication Réseau

**Responsabilités :**
- Chargement des messages de test binaires
- Connexion TCP vers automates
- Envoi/réception avec timeouts
- Analyse des réponses ACK

**Fonctions Clés :**
- `load_test_messages()` - Charge les fichiers .bin
- `perform_single_test()` - Test complet d'un message
- `analyze_ack_response()` - Parse les réponses HL7

### 4. src/logging.c - Système de Logging

**Responsabilités :**
- Logs applicatifs avec rotation quotidienne
- Logs d'alertes séparés
- Niveaux de log configurables
- Thread-safety

**Fonctions Principales :**
```c
void log_message(const char* level, const char* format, ...);
void log_alert(const char* format, ...);
```

### 5. src/monitoring.c - Statistiques

**Responsabilités :**
- Compteurs de succès/échecs
- Calcul des taux de réussite
- Détection des pannes consécutives
- Génération d'alertes

### 6. src/utils.c - Utilitaires

**Responsabilités :**
- Gestion des signaux (SIGINT, SIGTERM)
- Validation et nettoyage des chemins
- Fonctions d'erreur et de nettoyage
- Utilitaires de chaînes

## 🔒 Sécurité Implémentée

### Protection Path Traversal
```c
int validate_file_path(const char* path, const char* base_dir) {
    char* real_path = realpath(path, NULL);
    char* real_base = realpath(base_dir, NULL);
    
    // Vérification que le chemin reste dans base_dir
    int result = (strncmp(real_path, real_base, strlen(real_base)) == 0);
    
    free(real_path);
    free(real_base);
    return result;
}
```

### Gestion des Signaux
```c
void signal_handler(int sig) {
    switch(sig) {
        case SIGINT:
        case SIGTERM:
            running = false;  // Arrêt gracieux
            break;
        case SIGHUP:
            reload_config = true;  // Rechargement config
            break;
    }
}
```

## 🌐 Protocole de Communication

### Format des Messages HL7

Les messages de test sont stockés en binaire dans `messages/` :
- `m1.bin` à `m8.bin` - Messages de test variés
- `m_query.bin` - Message de requête standard

### Analyse des Réponses

```c
int analyze_ack_response(const char* ack_buffer, size_t ack_size) {
    // Recherche du segment MSA (Message Acknowledgment)
    if (strstr(ack_buffer, "MSA|AA")) return 1;  // Succès
    if (strstr(ack_buffer, "MSA|AE")) return 0;  // Erreur
    if (strstr(ack_buffer, "MSA|AR")) return 0;  // Rejet
    return -1;  // Format invalide
}
```

## 🔄 Flux d'Exécution

### 1. Initialisation
```
main() → init_logging() → setup_signal_handlers() → load_config()
```

### 2. Chargement des Messages
```
load_test_messages() → validation des fichiers .bin → stockage en mémoire
```

### 3. Boucle de Test
```
Pour chaque message:
  ├── Connexion TCP vers automate
  ├── Envoi du message binaire
  ├── Attente réponse (avec timeout)
  ├── Analyse de la réponse ACK
  ├── Mise à jour des statistiques
  └── Log du résultat
```

### 4. Monitoring
```
Après chaque cycle:
  ├── Calcul des statistiques
  ├── Détection des pannes
  ├── Génération d'alertes si nécessaire
  └── Affichage du rapport
```

## 🛠️ Compilation et Build

### Système de Build Dual

**CMake (Recommandé) :**
- Configuration professionnelle
- Support multi-plateforme
- Gestion automatique des dépendances

**Makefile (Alternative) :**
- Compilation rapide
- Modes debug/release
- Règles personnalisées

### Flags de Compilation
```c
// Version et build info intégrées
#define VERSION "2.0.0"
#define BUILD_DATE __DATE__
#define BUILD_TIME __TIME__
```

## 🧪 Tests et Validation

### Tests Automatisés
- `tests/test_suite.sh` - Suite complète
- `tests/test_refactoring.sh` - Validation structure
- `tests/test_path_traversal_fix.sh` - Tests sécurité

### Validation Continue
```bash
# Compilation et tests
scripts/dev.sh build test

# Analyse statique
scripts/dev.sh analyze

# Formatage du code
scripts/dev.sh format
```

## 📊 Métriques et Monitoring

### Statistiques Collectées
- Nombre total de tests
- Taux de succès/échec
- Temps de réponse moyens
- Pannes consécutives
- Uptime du système

### Alertes Automatiques
- Panne détectée (3 échecs consécutifs)
- Taux d'échec > 50%
- Timeout de connexion répétés
- Erreurs système critiques

## 🔮 Extensibilité

### Ajout de Nouveaux Modules
1. Créer `src/nouveau_module.c`
2. Créer `include/nouveau_module.h`
3. Ajouter à `include/server_tester.h`
4. Mettre à jour CMakeLists.txt et Makefile

### Support de Nouveaux Protocoles
- Étendre `src/network.c`
- Ajouter parsers spécifiques
- Configurer via INI

### Intégration Monitoring Externe
- Métriques Prometheus
- API REST pour status
- Webhooks pour alertes

## 🎯 Bonnes Pratiques

### Code Style
- Standard C11 strict
- Noms explicites et cohérents
- Commentaires pour la logique complexe
- Gestion d'erreur systématique

### Gestion Mémoire
- Libération systématique des ressources
- Vérification des allocations
- Pas de fuites mémoire (validé Valgrind)

### Logging
- Niveaux appropriés (INFO, WARN, ERROR)
- Messages informatifs et actionnables
- Rotation automatique des logs

---

**Ce guide couvre les aspects essentiels de l'implémentation. Pour des détails spécifiques, consulter les commentaires dans le code source.**
