#!/bin/bash

# Script d'installation pour Red Hat Enterprise Linux avec Podman
# Testeur automatisé d'automate de biologie médicale pour iMX8 mini

set -e

# Configuration
INSTALL_DIR="/opt/automate_tester"
SERVICE_NAME="automate-tester-podman"
LOG_DIR="/var/log/automate_tester"
DATA_DIR="/opt/shared-data"

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction de logging
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2
}

log_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

# Fonction d'aide
show_help() {
    echo "Script d'installation pour Red Hat Enterprise Linux avec Podman"
    echo "Testeur automatisé d'automate de biologie médicale pour iMX8 mini"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --install         Installer le service (par défaut)"
    echo "  --uninstall       Désinstaller le service"
    echo "  --update          Mettre à jour le service"
    echo "  --install-dir DIR Répertoire d'installation (défaut: $INSTALL_DIR)"
    echo "  --log-dir DIR     Répertoire des logs (défaut: $LOG_DIR)"
    echo "  --data-dir DIR    Répertoire des données (défaut: $DATA_DIR)"
    echo "  -h, --help        Afficher cette aide"
    echo ""
    echo "Exemples:"
    echo "  sudo $0                    # Installation complète"
    echo "  sudo $0 --uninstall       # Désinstallation"
    echo "  sudo $0 --update          # Mise à jour"
}

# Vérifier les privilèges root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "Ce script doit être exécuté en tant que root"
        echo "Utilisez: sudo $0"
        exit 1
    fi
}

# Détecter la distribution Red Hat
detect_redhat() {
    if [[ -f /etc/redhat-release ]]; then
        local version=$(cat /etc/redhat-release)
        log "Distribution détectée: $version"
        
        # Vérifier la version
        if grep -q "release 9" /etc/redhat-release; then
            log_success "Red Hat Enterprise Linux 9 détecté (compatible)"
        elif grep -q "release 8" /etc/redhat-release; then
            log_success "Red Hat Enterprise Linux 8 détecté (compatible)"
        else
            log_warning "Version Red Hat non testée, continuons..."
        fi
    else
        log_error "Ce script est conçu pour Red Hat Enterprise Linux"
        exit 1
    fi
}

# Installer les dépendances
install_dependencies() {
    log "Installation des dépendances Red Hat..."
    
    # Mettre à jour le système
    dnf update -y
    
    # Installer EPEL si nécessaire
    if ! dnf repolist | grep -q epel; then
        log "Installation d'EPEL..."
        dnf install -y https://dl.fedoraproject.org/pub/epel/epel-release-latest-9.noarch.rpm || \
        dnf install -y https://dl.fedoraproject.org/pub/epel/epel-release-latest-8.noarch.rpm
    fi
    
    # Installer Podman et outils
    log "Installation de Podman et outils..."
    dnf install -y \
        podman \
        podman-compose \
        buildah \
        skopeo \
        git \
        curl \
        wget \
        tar \
        gzip \
        policycoreutils-python-utils \
        selinux-policy-devel
    
    # Vérifier l'installation
    podman --version
    log_success "Dépendances installées"
}

# Configurer SELinux
configure_selinux() {
    log "Configuration SELinux pour Podman..."
    
    # Activer les booléens SELinux nécessaires
    setsebool -P container_manage_cgroup on
    setsebool -P virt_use_nfs on
    setsebool -P virt_use_samba on
    
    # Créer les contextes SELinux pour les répertoires
    semanage fcontext -a -t container_file_t "$INSTALL_DIR(/.*)?" 2>/dev/null || true
    semanage fcontext -a -t container_file_t "$LOG_DIR(/.*)?" 2>/dev/null || true
    semanage fcontext -a -t container_file_t "$DATA_DIR(/.*)?" 2>/dev/null || true
    
    log_success "SELinux configuré"
}

# Créer les répertoires système
create_directories() {
    log "Création des répertoires système..."
    
    # Créer les répertoires
    mkdir -p "$INSTALL_DIR"
    mkdir -p "$LOG_DIR"
    mkdir -p "$DATA_DIR"
    mkdir -p "$INSTALL_DIR/logs"
    
    # Configurer les permissions
    chmod 755 "$INSTALL_DIR"
    chmod 755 "$LOG_DIR"
    chmod 755 "$DATA_DIR"
    
    # Appliquer les contextes SELinux
    restorecon -R "$INSTALL_DIR" 2>/dev/null || true
    restorecon -R "$LOG_DIR" 2>/dev/null || true
    restorecon -R "$DATA_DIR" 2>/dev/null || true
    
    log_success "Répertoires créés"
}

# Installer les fichiers
install_files() {
    log "Installation des fichiers dans $INSTALL_DIR..."
    
    # Copier les fichiers
    cp server_tester.c "$INSTALL_DIR/"
    cp CMakeLists.txt "$INSTALL_DIR/"
    cp Dockerfile "$INSTALL_DIR/"
    cp start_podman.sh "$INSTALL_DIR/"
    cp README.md "$INSTALL_DIR/"
    
    # Copier la configuration
    if [[ ! -f "$INSTALL_DIR/server_tester.ini" ]]; then
        cp server_tester.example.ini "$INSTALL_DIR/server_tester.ini"
        log "Configuration d'exemple copiée"
        log_warning "IMPORTANT: Éditez $INSTALL_DIR/server_tester.ini avec vos paramètres"
    fi
    
    # Copier les messages de test
    cp -r messages/ "$INSTALL_DIR/"
    
    # Rendre les scripts exécutables
    chmod +x "$INSTALL_DIR/start_podman.sh"
    
    # Appliquer les contextes SELinux
    restorecon -R "$INSTALL_DIR" 2>/dev/null || true
    
    log_success "Fichiers installés"
}

# Construire l'image Podman
build_image() {
    log "Construction de l'image Podman..."
    
    cd "$INSTALL_DIR"
    
    # Construire l'image
    podman build \
        --tag automate_tester:latest \
        --file Dockerfile \
        --format docker \
        .
    
    if [[ $? -eq 0 ]]; then
        log_success "Image Podman construite avec succès"
    else
        log_error "Échec de la construction de l'image"
        exit 1
    fi
}

# Installer le service systemd
install_service() {
    log "Installation du service systemd..."
    
    # Créer le fichier de service
    cat > "/etc/systemd/system/$SERVICE_NAME.service" << EOF
[Unit]
Description=Testeur Automatisé pour Driver d'Automate de Biologie Médicale (Podman)
Documentation=file://$INSTALL_DIR/README.md
Wants=network-online.target
After=network-online.target
RequiresMountsFor=%t/containers

[Service]
Environment=PODMAN_SYSTEMD_UNIT=%n
Restart=on-failure
RestartSec=30
TimeoutStopSec=70
ExecStartPre=/bin/rm -f %t/%n.ctr-id
ExecStart=/usr/bin/podman run \\
    --cidfile=%t/%n.ctr-id \\
    --cgroups=no-conmon \\
    --rm \\
    --sdnotify=conmon \\
    --replace \\
    --name automate_tester_service \\
    --network host \\
    -v $LOG_DIR:/opt/automate_tester/var/logs:Z \\
    -v $DATA_DIR:/opt/automate_tester/var/data:Z \\
    -v $INSTALL_DIR/server_tester.ini:/opt/automate_tester/etc/config.ini:ro,Z \\
    -e TZ=Europe/Paris \\
    automate_tester:latest
ExecStop=/usr/bin/podman stop --ignore --cidfile=%t/%n.ctr-id
ExecStopPost=/usr/bin/podman rm -f --ignore --cidfile=%t/%n.ctr-id
Type=notify
NotifyAccess=all

# Sécurité
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$LOG_DIR $DATA_DIR
ReadOnlyPaths=$INSTALL_DIR

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

[Install]
WantedBy=multi-user.target
EOF

    # Recharger systemd
    systemctl daemon-reload
    
    # Activer le service
    systemctl enable "$SERVICE_NAME"
    
    log_success "Service systemd installé et activé"
}

# Désinstaller le service
uninstall_service() {
    log "Désinstallation du service..."
    
    # Arrêter le service
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        systemctl stop "$SERVICE_NAME"
    fi
    
    # Désactiver le service
    if systemctl is-enabled --quiet "$SERVICE_NAME"; then
        systemctl disable "$SERVICE_NAME"
    fi
    
    # Supprimer le fichier de service
    if [[ -f "/etc/systemd/system/$SERVICE_NAME.service" ]]; then
        rm "/etc/systemd/system/$SERVICE_NAME.service"
    fi
    
    # Recharger systemd
    systemctl daemon-reload
    
    # Arrêter et supprimer les conteneurs Podman
    podman stop automate_tester_service 2>/dev/null || true
    podman rm automate_tester_service 2>/dev/null || true
    podman rmi automate_tester:latest 2>/dev/null || true
    
    # Supprimer les fichiers d'installation
    if [[ -d "$INSTALL_DIR" ]]; then
        log_warning "Suppression de $INSTALL_DIR..."
        rm -rf "$INSTALL_DIR"
    fi
    
    log_success "Service désinstallé"
}

# Mettre à jour le service
update_service() {
    log "Mise à jour du service..."
    
    # Arrêter le service
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        systemctl stop "$SERVICE_NAME"
    fi
    
    # Sauvegarder la configuration
    if [[ -f "$INSTALL_DIR/server_tester.ini" ]]; then
        cp "$INSTALL_DIR/server_tester.ini" "/tmp/server_tester.ini.backup"
        log "Configuration sauvegardée"
    fi
    
    # Installer les nouveaux fichiers
    install_files
    
    # Restaurer la configuration
    if [[ -f "/tmp/server_tester.ini.backup" ]]; then
        cp "/tmp/server_tester.ini.backup" "$INSTALL_DIR/server_tester.ini"
        rm "/tmp/server_tester.ini.backup"
        log "Configuration restaurée"
    fi
    
    # Reconstruire l'image
    build_image
    
    # Redémarrer le service
    systemctl start "$SERVICE_NAME"
    
    log_success "Service mis à jour"
}

# Variables par défaut
INSTALL_MODE="install"

# Traitement des arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --install)
            INSTALL_MODE="install"
            shift
            ;;
        --uninstall)
            INSTALL_MODE="uninstall"
            shift
            ;;
        --update)
            INSTALL_MODE="update"
            shift
            ;;
        --install-dir)
            INSTALL_DIR="$2"
            shift 2
            ;;
        --log-dir)
            LOG_DIR="$2"
            shift 2
            ;;
        --data-dir)
            DATA_DIR="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            log_error "Option inconnue: $1"
            show_help
            exit 1
            ;;
    esac
done

# Vérifications préliminaires
check_root
detect_redhat

# Exécution selon le mode
case $INSTALL_MODE in
    install)
        log "=== Installation du testeur automatisé (Red Hat + Podman) ==="
        install_dependencies
        configure_selinux
        create_directories
        install_files
        build_image
        install_service
        log_success "Installation terminée"
        
        echo ""
        echo "=== Instructions post-installation ==="
        echo "1. Éditez la configuration: $INSTALL_DIR/server_tester.ini"
        echo "2. Démarrez le service: systemctl start $SERVICE_NAME"
        echo "3. Vérifiez le statut: systemctl status $SERVICE_NAME"
        echo "4. Consultez les logs: journalctl -u $SERVICE_NAME -f"
        echo "5. Gestion Podman: $INSTALL_DIR/start_podman.sh --help"
        ;;
    uninstall)
        log "=== Désinstallation du testeur automatisé ==="
        uninstall_service
        log_success "Désinstallation terminée"
        ;;
    update)
        log "=== Mise à jour du testeur automatisé ==="
        update_service
        log_success "Mise à jour terminée"
        ;;
esac
