#!/bin/bash

# Script de déploiement automatisé pour drivers_automates
# Usage: ./deploy.sh

set -e  # Arrêter le script en cas d'erreur

# Couleurs pour l'affichage
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

print_step() {
    echo -e "${GREEN}[STEP]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Variables
PROJECT_DIR="/home/<USER>/vscode/drivers_automates"
PARENT_DIR="/home/<USER>/vscode"
BUILD_DIR="$PROJECT_DIR/build"
MAIN_C_FILE="$PROJECT_DIR/src/main.c"
TAR_ARCHIVE_NAME="drivers_automates.tar.gz"
TAR_ARCHIVE_PATH="$PARENT_DIR/$TAR_ARCHIVE_NAME"

# Vérifier qu'on est dans le bon répertoire
if [ ! -f "$MAIN_C_FILE" ]; then
    print_error "Fichier main.c non trouvé dans $PROJECT_DIR/src/. Assurez-vous d'être dans le bon répertoire."
    exit 1
fi

# 1. Mettre à jour la version dans main.c
print_step "Mise à jour de la version..."
NEW_VERSION="1.0.$(date +%Y%m%d.%H%M)"
if ! sed -i "s/#define VERSION \".*\"/#define VERSION \"$NEW_VERSION\"/" "$MAIN_C_FILE"; then
    print_error "Échec de la mise à jour de la version dans $MAIN_C_FILE."
    exit 1
fi
echo "Version mise à jour : $NEW_VERSION"

# 2. Aller au répertoire parent
print_step "Navigation vers le répertoire parent : $PARENT_DIR"
cd "$PARENT_DIR" || { print_error "Impossible de naviguer vers $PARENT_DIR."; exit 1; }

# 3. Supprimer le dossier build s'il existe
if [ -d "$BUILD_DIR" ]; then
    print_step "Suppression du dossier build : $BUILD_DIR"
    if ! rm -rf "$BUILD_DIR"; then
        print_error "Échec de la suppression du dossier build."
        exit 1
    fi
    echo "Dossier build supprimé"
else
    print_warning "Dossier build n'existe pas ($BUILD_DIR), rien à supprimer."
fi

# Supprimer l'ancienne archive si elle existe
if [ -f "$TAR_ARCHIVE_PATH" ]; then
    print_step "Suppression de l'ancienne archive $TAR_ARCHIVE_NAME..."
    if ! rm -f "$TAR_ARCHIVE_PATH"; then
        print_error "Échec de la suppression de l'ancienne archive."
        exit 1
    fi
    echo "Ancienne archive supprimée."
fi

# 4. Créer l'archive tar.gz avec exclusions
print_step "Création de l'archive tar.gz : $TAR_ARCHIVE_NAME"
if ! tar -czf "$TAR_ARCHIVE_NAME" \
    --exclude="drivers_automates/.git" \
    --exclude="drivers_automates/.gitignore" \
    --exclude="drivers_automates/build" \
    --exclude="drivers_automates/cmake-build-debug" \
    --exclude="drivers_automates/cmake-build-release" \
    --exclude="drivers_automates/.vscode" \
    --exclude="drivers_automates/.idea" \
    --exclude="drivers_automates/deploy.sh" \
    --exclude="drivers_automates/*.tar.gz" \
    --exclude="drivers_automates/*.log" \
    --exclude="drivers_automates/logs" \
    --exclude="drivers_automates/messages*" \
    --exclude="drivers_automates/vgcore.*" \
    drivers_automates/; then
    print_error "Échec de la création de l'archive tar.gz."
    exit 1
fi
echo "Archive créée : $TAR_ARCHIVE_NAME"

# 5. Changer les permissions
print_step "Modification des permissions pour $TAR_ARCHIVE_NAME..."
if ! chmod 777 "$TAR_ARCHIVE_NAME"; then
    print_error "Échec de la modification des permissions sur $TAR_ARCHIVE_NAME."
    exit 1
fi
echo "Permissions changées pour 777."

# 6. Déplacer l'archive dans le dossier du projet
print_step "Déplacement de l'archive $TAR_ARCHIVE_NAME vers $PROJECT_DIR/"
if ! mv "$TAR_ARCHIVE_NAME" "$PROJECT_DIR/"; then
    print_error "Échec du déplacement de l'archive."
    exit 1
fi
echo "Archive déplacée."

# 7. Retourner dans le dossier du projet
print_step "Retour dans le dossier du projet : $PROJECT_DIR"
cd "$PROJECT_DIR" || { print_error "Impossible de retourner dans $PROJECT_DIR."; exit 1; }

# 8. Build du conteneur Podman
print_step "Build du conteneur Podman..."
if [ ! -f "Dockerfile" ]; then
    print_error "Dockerfile non trouvé dans $PROJECT_DIR. Impossible de construire l'image Podman."
    exit 1
fi
if ! podman build -t universaldriver:latest -f Dockerfile .; then
    print_error "Échec du build du conteneur Podman."
    exit 1
fi
echo "Conteneur Podman universaldriver:latest construit avec succès."

# NOUVELLE ÉTAPE : Supprimer les images Podman orphelines (<none> <none>)
print_step "Suppression des images Podman orphelines (<none> <none>)..."
DANGLING_IMAGES=$(podman images -f "dangling=true" -q)
if [ -n "$DANGLING_IMAGES" ]; then
    print_warning "Images orphelines trouvées et en cours de suppression : $(echo $DANGLING_IMAGES | wc -w) images."
    if ! podman rmi $DANGLING_IMAGES; then
        print_error "Échec de la suppression de certaines images orphelines."
        # Ne pas sortir ici, car le déploiement principal est réussi
    fi
    echo "Images orphelines supprimées."
else
    print_warning "Aucune image orpheline à supprimer."
fi

print_step "Déploiement terminé avec succès !"
echo -e "${GREEN}Version déployée : $NEW_VERSION${NC}"

# Afficher la taille de l'archive finale
ARCHIVE_SIZE=$(du -h "$PROJECT_DIR/$TAR_ARCHIVE_NAME" | cut -f1)
echo -e "${GREEN}Taille de l'archive : $ARCHIVE_SIZE${NC}"
echo -e "${GREEN}Image Podman taggée : drivers_automates:$CONTAINER_TAG${NC}"

# Après la construction de l'image, ajouter un exemple de lancement
echo ""
echo "=== Commande de lancement recommandée ==="
echo "# Créer le dossier partagé s'il n'existe pas"
echo "mkdir -p \$HOME/querydemog"
echo ""
echo "podman run -d \\"
echo "  -p 50901:50901 \\"
echo "  --name driver_cont \\"
echo "  -v \$HOME/querydemog:/querydemog \\"
echo "  localhost/universaldriver:latest"
echo ""
echo "Pour arrêter et supprimer le conteneur existant :"
echo "podman stop driver_cont && podman rm driver_cont"
