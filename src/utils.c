#include "server_tester.h"

// Variables globales pour la gestion des signaux
volatile sig_atomic_t keep_running = 1;
volatile sig_atomic_t reload_config = 0;

// Function to trim whitespace from string
char* trim(char* str) {
    char* end;

    // Remove leading spaces
    while (*str == ' ' || *str == '\t') str++;

    if (*str == 0) return str;

    // Remove trailing spaces
    end = str + strlen(str) - 1;
    while (end > str && (*end == ' ' || *end == '\t' || *end == '\n' || *end == '\r')) end--;

    end[1] = '\0';
    return str;
}

// Function to sanitize file path and prevent path traversal attacks
char* sanitize_path(const char* path) {
    if (path == NULL) return NULL;

    static char sanitized[PATH_MAX];

    // Copy the path and manually sanitize it
    strncpy(sanitized, path, sizeof(sanitized) - 1);
    sanitized[sizeof(sanitized) - 1] = '\0';

    // Remove any "../" sequences to prevent directory traversal
    char* pos;
    while ((pos = strstr(sanitized, "../")) != NULL) {
        memmove(pos, pos + 3, strlen(pos + 3) + 1);
    }

    // Remove any "./" sequences at the beginning
    while (strncmp(sanitized, "./", 2) == 0) {
        memmove(sanitized, sanitized + 2, strlen(sanitized + 2) + 1);
    }

    return sanitized;
}

// Function to validate file path against a base directory
int validate_file_path(const char* path, const char* base_dir) {
    if (path == NULL || base_dir == NULL) {
        return 0; // Invalid input
    }

    char* sanitized_path = sanitize_path(path);
    char* sanitized_base = sanitize_path(base_dir);

    if (sanitized_path == NULL || sanitized_base == NULL) {
        return 0; // Sanitization failed
    }

    // Check if the sanitized path starts with the base directory
    size_t base_len = strlen(sanitized_base);
    if (strncmp(sanitized_path, sanitized_base, base_len) != 0) {
        return 0; // Path is outside base directory
    }

    // Additional security checks
    // Reject paths containing null bytes
    if (strlen(path) != strcspn(path, "\0")) {
        return 0;
    }

    // Reject paths that are too long
    if (strlen(path) >= PATH_MAX) {
        return 0;
    }

    return 1; // Path is valid
}

// Function to handle errors
void error_exit(const char* message) {
    log_message("ERROR", "%s: %s", message, strerror(errno));
    fprintf(stderr, "Error: %s: %s\n", message, strerror(errno));
    close_logging();
    exit(1);
}

// Gestionnaires de signaux
void signal_handler(int sig) {
    switch(sig) {
        case SIGINT:
        case SIGTERM:
            log_message("INFO", "Received termination signal %d, shutting down gracefully", sig);
            keep_running = 0;
            break;
        case SIGHUP:
            log_message("INFO", "Received SIGHUP signal, reloading configuration");
            reload_config = 1;
            break;
        default:
            log_message("WARNING", "Received unexpected signal %d", sig);
            break;
    }
}

// Initialiser les gestionnaires de signaux
void setup_signal_handlers(void) {
    struct sigaction sa;
    sa.sa_handler = signal_handler;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = 0;

    sigaction(SIGINT, &sa, NULL);
    sigaction(SIGTERM, &sa, NULL);
    sigaction(SIGHUP, &sa, NULL);

    // Ignorer SIGPIPE pour éviter les crashes sur les connexions fermées
    signal(SIGPIPE, SIG_IGN);
}
