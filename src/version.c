#include "server_tester.h"

/**
 * @file version.c
 * @brief Version and build information
 */

const char* get_version(void) {
    return VERSION;
}

const char* get_build_info(void) {
    static char build_info[256];
    snprintf(build_info, sizeof(build_info), 
             "Version: %s, Built: %s %s, Compiler: %s",
             VERSION, __DATE__, __TIME__, 
#ifdef __GNUC__
             "GCC " __VERSION__
#else
             "Unknown"
#endif
    );
    return build_info;
}
