#include "server_tester.h"

// Function to load test messages from directory
int load_test_messages(const char* directory, TestMessage* messages, int max_messages) {
    DIR* dir;
    struct dirent* entry;
    struct stat file_stat;
    int count = 0;
    char full_path[MAX_FILENAME_LENGTH];

    dir = opendir(directory);
    if (dir == NULL) {
        log_message("ERROR", "Cannot open messages directory '%s': %s", directory, strerror(errno));
        return -1;
    }

    log_message("INFO", "Loading test messages from directory: %s", directory);

    while ((entry = readdir(dir)) != NULL && count < max_messages) {
        // Skip directories and hidden files
        if (entry->d_name[0] == '.' || entry->d_type == DT_DIR) {
            continue;
        }

        // Skip files that don't end with .bin
        char* ext = strrchr(entry->d_name, '.');
        if (ext == NULL || strcmp(ext, ".bin") != 0) {
            continue;
        }

        // Build full path
        snprintf(full_path, sizeof(full_path), "%s/%s", directory, entry->d_name);

        // Get file stats
        if (stat(full_path, &file_stat) != 0) {
            log_message("WARNING", "Cannot stat file '%s': %s", full_path, strerror(errno));
            continue;
        }

        // Store message info
        strncpy(messages[count].filename, entry->d_name, sizeof(messages[count].filename) - 1);
        messages[count].filename[sizeof(messages[count].filename) - 1] = '\0';

        strncpy(messages[count].filepath, full_path, sizeof(messages[count].filepath) - 1);
        messages[count].filepath[sizeof(messages[count].filepath) - 1] = '\0';

        messages[count].size = file_stat.st_size;

        log_message("INFO", "Loaded test message: %s (%zu bytes)", messages[count].filename, messages[count].size);
        count++;
    }

    closedir(dir);
    log_message("INFO", "Loaded %d test messages from directory", count);
    return count;
}

// Function to analyze acknowledgment response
int analyze_ack_response(const char* ack_buffer, size_t ack_size) {
    // Basic HL7 ACK analysis
    if (ack_size == 0) {
        log_message("WARNING", "Empty acknowledgment received");
        return 0;
    }

    // Check for HL7 MSA segment (Message Acknowledgment)
    if (strstr(ack_buffer, "MSA") != NULL) {
        // Look for acknowledgment code
        if (strstr(ack_buffer, "MSA|AA") != NULL) {
            log_message("INFO", "Positive acknowledgment received (AA - Application Accept)");
            return 1;
        } else if (strstr(ack_buffer, "MSA|AE") != NULL) {
            log_message("WARNING", "Application error acknowledgment received (AE)");
            return 0;
        } else if (strstr(ack_buffer, "MSA|AR") != NULL) {
            log_message("WARNING", "Application reject acknowledgment received (AR)");
            return 0;
        }
    }

    // Check for simple positive responses
    if (strstr(ack_buffer, "OK") != NULL || strstr(ack_buffer, "ACK") != NULL) {
        log_message("INFO", "Simple positive acknowledgment received");
        return 1;
    }

    // Check for error indicators
    if (strstr(ack_buffer, "ERROR") != NULL || strstr(ack_buffer, "FAIL") != NULL) {
        log_message("WARNING", "Error indication in acknowledgment");
        return 0;
    }

    // Default: consider any non-empty response as positive
    log_message("INFO", "Non-standard acknowledgment received, considering as positive");
    return 1;
}

// Function to perform a single test with retry logic
int perform_single_test(const Config* config, const TestMessage* message) {
    int sockfd;
    struct sockaddr_in server_addr;
    char* buffer;
    char* ack_buffer;
    struct timeval timeout;
    FILE *file;
    ssize_t bytes_sent, bytes_received;
    int success = 0;
    int attempt;

    // Allocate buffers
    buffer = malloc(config->max_buffer_size);
    ack_buffer = malloc(config->max_ack_size);
    if (buffer == NULL || ack_buffer == NULL) {
        log_message("ERROR", "Memory allocation failed for test");
        if (buffer) free(buffer);
        if (ack_buffer) free(ack_buffer);
        return 0;
    }

    // Read message file
    file = fopen(message->filepath, "rb");
    if (file == NULL) {
        log_message("ERROR", "Cannot open test message file '%s': %s", message->filepath, strerror(errno));
        free(buffer);
        free(ack_buffer);
        return 0;
    }

    if (message->size > (size_t)config->max_buffer_size) {
        log_message("ERROR", "Test message too large (%zu bytes, max: %d bytes)", message->size, config->max_buffer_size);
        fclose(file);
        free(buffer);
        free(ack_buffer);
        return 0;
    }

    size_t bytes_read = fread(buffer, 1, message->size, file);
    fclose(file);

    if (bytes_read != message->size) {
        log_message("ERROR", "Error reading test message file: expected %zu bytes, read %zu bytes", message->size, bytes_read);
        free(buffer);
        free(ack_buffer);
        return 0;
    }

    // Retry logic
    for (attempt = 1; attempt <= config->max_retry_attempts && !success; attempt++) {
        log_message("INFO", "Test attempt %d/%d for message '%s'", attempt, config->max_retry_attempts, message->filename);

        // Create socket
        sockfd = socket(AF_INET, SOCK_STREAM, 0);
        if (sockfd < 0) {
            log_message("ERROR", "Error creating socket (attempt %d): %s", attempt, strerror(errno));
            if (attempt < config->max_retry_attempts) {
                sleep(config->retry_delay_seconds);
            }
            continue;
        }

        // Configure timeout
        timeout.tv_sec = config->ack_timeout;
        timeout.tv_usec = 0;
        if (setsockopt(sockfd, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) < 0) {
            log_message("ERROR", "Error setting socket timeout (attempt %d): %s", attempt, strerror(errno));
            close(sockfd);
            if (attempt < config->max_retry_attempts) {
                sleep(config->retry_delay_seconds);
            }
            continue;
        }

        // Configure server address
        memset(&server_addr, 0, sizeof(server_addr));
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(config->server_port);
        if (inet_pton(AF_INET, config->server_ip, &server_addr.sin_addr) <= 0) {
            log_message("ERROR", "Invalid server IP address: %s", config->server_ip);
            close(sockfd);
            break; // No point in retrying for invalid IP
        }

        // Connect to server
        if (connect(sockfd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
            log_message("ERROR", "Connection failed (attempt %d) to %s:%d: %s",
                       attempt, config->server_ip, config->server_port, strerror(errno));
            close(sockfd);
            if (attempt < config->max_retry_attempts) {
                sleep(config->retry_delay_seconds);
            }
            continue;
        }

        log_message("INFO", "Connected to %s:%d (attempt %d)", config->server_ip, config->server_port, attempt);

        // Send message
        bytes_sent = send(sockfd, buffer, message->size, 0);
        if (bytes_sent < 0) {
            log_message("ERROR", "Failed to send data (attempt %d): %s", attempt, strerror(errno));
            close(sockfd);
            if (attempt < config->max_retry_attempts) {
                sleep(config->retry_delay_seconds);
            }
            continue;
        }

        if ((size_t)bytes_sent != message->size) {
            log_message("ERROR", "Partial data sent (attempt %d): %zd bytes sent out of %zu bytes",
                       attempt, bytes_sent, message->size);
            close(sockfd);
            if (attempt < config->max_retry_attempts) {
                sleep(config->retry_delay_seconds);
            }
            continue;
        }

        log_message("INFO", "Data sent successfully (attempt %d): %zd bytes", attempt, bytes_sent);

        // Receive acknowledgment
        bytes_received = recv(sockfd, ack_buffer, config->max_ack_size - 1, 0);
        if (bytes_received < 0) {
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                log_message("ERROR", "Acknowledgment timeout (attempt %d) after %d seconds", attempt, config->ack_timeout);
            } else {
                log_message("ERROR", "Error receiving acknowledgment (attempt %d): %s", attempt, strerror(errno));
            }
            close(sockfd);
            if (attempt < config->max_retry_attempts) {
                sleep(config->retry_delay_seconds);
            }
            continue;
        }

        if (bytes_received == 0) {
            log_message("ERROR", "Connection closed by server without acknowledgment (attempt %d)", attempt);
            close(sockfd);
            if (attempt < config->max_retry_attempts) {
                sleep(config->retry_delay_seconds);
            }
            continue;
        }

        // Analyze acknowledgment
        ack_buffer[bytes_received] = '\0';
        log_message("INFO", "Acknowledgment received (attempt %d, %zd bytes): %s", attempt, bytes_received, ack_buffer);

        if (analyze_ack_response(ack_buffer, bytes_received)) {
            success = 1;
            log_message("INFO", "Test successful for message '%s' (attempt %d)", message->filename, attempt);
        } else {
            log_message("WARNING", "Test failed for message '%s' - negative acknowledgment (attempt %d)", message->filename, attempt);
        }

        close(sockfd);

        if (!success && attempt < config->max_retry_attempts) {
            sleep(config->retry_delay_seconds);
        }
    }

    free(buffer);
    free(ack_buffer);

    if (!success) {
        log_message("ERROR", "Test failed for message '%s' after %d attempts", message->filename, config->max_retry_attempts);
    }

    return success;
}

// Function to perform health check
int perform_health_check(const Config* config) {
    int sockfd;
    struct sockaddr_in server_addr;
    struct timeval timeout;

    // Create socket
    sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd < 0) {
        log_message("WARNING", "Health check: Error creating socket: %s", strerror(errno));
        return 0;
    }

    // Set a shorter timeout for health checks
    timeout.tv_sec = 5;  // 5 seconds timeout for health checks
    timeout.tv_usec = 0;
    if (setsockopt(sockfd, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) < 0 ||
        setsockopt(sockfd, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout)) < 0) {
        log_message("WARNING", "Health check: Error setting socket timeout: %s", strerror(errno));
        close(sockfd);
        return 0;
    }

    // Configure server address
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(config->server_port);
    if (inet_pton(AF_INET, config->server_ip, &server_addr.sin_addr) <= 0) {
        log_message("WARNING", "Health check: Invalid server IP address: %s", config->server_ip);
        close(sockfd);
        return 0;
    }

    // Try to connect
    if (connect(sockfd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        log_message("WARNING", "Health check: Connection failed to %s:%d: %s",
                   config->server_ip, config->server_port, strerror(errno));
        close(sockfd);
        return 0;
    }

    log_message("INFO", "Health check: Server %s:%d is reachable", config->server_ip, config->server_port);
    close(sockfd);
    return 1;
}
