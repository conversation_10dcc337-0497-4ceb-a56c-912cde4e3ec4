#ifndef SERVER_TESTER_H
#define SERVER_TESTER_H

/**
 * @file server_tester.h
 * @brief Main header file for Automated Driver Tester
 * @version 2.0.0
 * @date 2025-01-11
 * 
 * This is the main header file that includes all necessary components
 * for the Automated Driver Tester application.
 */

// Version information
#define SERVER_TESTER_VERSION_MAJOR 2
#define SERVER_TESTER_VERSION_MINOR 0
#define SERVER_TESTER_VERSION_PATCH 0

#ifndef VERSION
#define VERSION "2.0.0"
#endif

// Core components
#include "common.h"
#include "utils.h"
#include "logging.h"
#include "config.h"
#include "network.h"
#include "monitoring.h"

// Application information
extern const char* get_version(void);
extern const char* get_build_info(void);

#endif // SERVER_TESTER_H
