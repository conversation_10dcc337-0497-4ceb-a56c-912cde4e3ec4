#ifndef CONFIG_DEFAULTS_H
#define CONFIG_DEFAULTS_H

/**
 * @file config_defaults.h
 * @brief Default configuration values and compile-time constants
 * @version 2.0.0
 * 
 * This file contains default values that can be overridden at compile time
 * or through configuration files.
 */

// Default configuration file paths
#ifndef DEFAULT_CONFIG_FILE
#define DEFAULT_CONFIG_FILE "server_tester.ini"
#endif

#ifndef DEFAULT_LOG_DIR
#define DEFAULT_LOG_DIR "logs"
#endif

#ifndef DEFAULT_MESSAGES_DIR
#define DEFAULT_MESSAGES_DIR "messages"
#endif

#ifndef DEFAULT_ALERT_LOG_FILE
#define DEFAULT_ALERT_LOG_FILE "logs/alerts.log"
#endif

// Default network settings
#ifndef DEFAULT_SERVER_IP
#define DEFAULT_SERVER_IP "127.0.0.1"
#endif

#ifndef DEFAULT_SERVER_PORT
#define DEFAULT_SERVER_PORT 50901
#endif

#ifndef DEFAULT_ACK_TIMEOUT
#define DEFAULT_ACK_TIMEOUT 5
#endif

#ifndef DEFAULT_MAX_ACK_SIZE
#define DEFAULT_MAX_ACK_SIZE 1024
#endif

#ifndef DEFAULT_MAX_BUFFER_SIZE
#define DEFAULT_MAX_BUFFER_SIZE 65536
#endif

// Default timing settings
#ifndef DEFAULT_TEST_INTERVAL_HOURS
#define DEFAULT_TEST_INTERVAL_HOURS 1
#endif

#ifndef DEFAULT_HEALTH_CHECK_ENABLED
#define DEFAULT_HEALTH_CHECK_ENABLED 1
#endif

#ifndef DEFAULT_MAX_RETRY_ATTEMPTS
#define DEFAULT_MAX_RETRY_ATTEMPTS 3
#endif

#ifndef DEFAULT_RETRY_DELAY_SECONDS
#define DEFAULT_RETRY_DELAY_SECONDS 5
#endif

// Compile-time feature flags
#ifndef ENABLE_DEBUG_LOGGING
#ifdef DEBUG
#define ENABLE_DEBUG_LOGGING 1
#else
#define ENABLE_DEBUG_LOGGING 0
#endif
#endif

#ifndef ENABLE_PERFORMANCE_METRICS
#define ENABLE_PERFORMANCE_METRICS 1
#endif

#ifndef ENABLE_SECURITY_FEATURES
#define ENABLE_SECURITY_FEATURES 1
#endif

#endif // CONFIG_DEFAULTS_H
