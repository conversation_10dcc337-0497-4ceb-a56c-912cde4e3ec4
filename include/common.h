#ifndef COMMON_H
#define COMMON_H

#define _POSIX_C_SOURCE 200809L
#define _GNU_SOURCE

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <errno.h>
#include <fcntl.h>
#include <time.h>
#include <sys/stat.h>
#include <stdarg.h>
#include <signal.h>
#include <sys/time.h>
#include <dirent.h>
#include <limits.h>
#include <libgen.h>

// Include default configuration
#include "config_defaults.h"

#ifndef PATH_MAX
#define PATH_MAX 4096
#endif

// Configuration constants
#define CONFIG_FILE "server_tester.ini"
#define MAX_LINE_LENGTH 256
#define LOG_DIR "logs"
#define MAX_LOG_PATH 512
#define MAX_MESSAGES 100
#define MAX_FILENAME_LENGTH 256
#define HEALTH_CHECK_INTERVAL 30  // seconds
#define MAX_CONSECUTIVE_FAILURES 3

// Structure pour stocker la configuration
typedef struct {
    char server_ip[16];
    int server_port;
    int ack_timeout;
    int max_ack_size;
    int max_buffer_size;
    int test_interval_hours;
    int health_check_enabled;
    int max_retry_attempts;
    int retry_delay_seconds;
    char messages_directory[256];
    char alert_log_file[256];
} Config;

// Structure pour stocker les informations d'un message de test
typedef struct {
    char filename[MAX_FILENAME_LENGTH];
    char filepath[MAX_FILENAME_LENGTH];
    size_t size;
} TestMessage;

// Structure pour les statistiques de monitoring
typedef struct {
    int total_tests;
    int successful_tests;
    int failed_tests;
    int consecutive_failures;
    time_t last_success;
    time_t last_failure;
    time_t start_time;
} MonitoringStats;

// Variables globales pour la gestion des signaux
extern volatile sig_atomic_t keep_running;
extern volatile sig_atomic_t reload_config;

// Variables globales pour les statistiques
extern MonitoringStats stats;

#endif // COMMON_H
