# Automated Driver Tester

**Testeur automatisé pour drivers d'automates de biologie médicale**

Programme C professionnel pour tester et monitorer la connectivité des drivers de communication médicale (HL7, POCT) en continu. Optimisé pour déploiement sur Red Hat Enterprise Linux avec Podman.

## 🚀 Démarrage Rapide

```bash
# Compilation
make release

# Configuration
cp config/server_tester.example.ini server_tester.ini
# Éditer server_tester.ini avec vos paramètres

# Exécution
./build/bin/server_tester

# Aide et options
./build/bin/server_tester --help
```

## ✨ Fonctionnalités

- **Tests automatisés** : Messages binaires HL7/POCT vers automates
- **Monitoring continu** : Statistiques temps réel et alertes
- **Configuration flexible** : Fichiers INI, modes daemon/single-shot
- **Sécurité renforcée** : Validation stricte, protection path traversal
- **Logs détaillés** : Rotation automatique, niveaux configurables
- **Déploiement Red Hat** : Podman, SELinux, systemd

## 🏗️ Structure du Projet

```
automate_tester/
├── main.c                  # Point d'entrée principal
├── src/                    # Modules sources
├── include/                # Headers
├── config/                 # Fichiers de configuration
├── scripts/                # Scripts utilitaires
├── tests/                  # Tests et validation
├── docs/                   # Documentation détaillée
├── messages/               # Messages de test HL7
└── build/                  # Artefacts de compilation
```

## 🔧 Compilation

### Méthode Recommandée (CMake)
```bash
make cmake
```

### Méthode Alternative (Make)
```bash
make release    # Version optimisée
make debug      # Version debug
```

### Script de Développement
```bash
scripts/dev.sh build       # Compilation
scripts/dev.sh test        # Tests
scripts/dev.sh info        # Informations projet
```

## ⚙️ Configuration

Le fichier `server_tester.ini` contient tous les paramètres :

```ini
[server]
ip = *************
port = 50901

[testing]
interval_hours = 1
max_retry = 3

[logging]
alert_log = logs/alerts.log
```

Exemples de configuration dans `config/` :
- `server_tester.example.ini` - Configuration de base
- `server_tester.redhat.ini` - Optimisée Red Hat

## 🐳 Déploiement

### Conteneur Podman
```bash
scripts/start_podman.sh
```

### Installation Système
```bash
scripts/install_redhat.sh
```

## 📊 Monitoring

Le programme génère :
- **Logs applicatifs** : `logs/server_tester_YYYYMMDD.log`
- **Logs d'alertes** : `logs/alerts.log`
- **Statistiques temps réel** : Affichage console

## 🧪 Tests

```bash
# Tests complets
tests/test_suite.sh

# Test de refactorisation
tests/test_refactoring.sh

# Tests de sécurité
tests/test_path_traversal_fix.sh
```

## 📚 Documentation

- **[IMPLEMENTATION.md](IMPLEMENTATION.md)** - Guide d'implémentation détaillé
- **[docs/ARCHITECTURE_v2.md](docs/ARCHITECTURE_v2.md)** - Architecture technique
- **[docs/README_REDHAT.md](docs/README_REDHAT.md)** - Guide Red Hat spécifique
- **[docs/SECURITY_FIX_REPORT.md](docs/SECURITY_FIX_REPORT.md)** - Correctifs sécurité

## 🔒 Sécurité

- Validation stricte des chemins de fichiers
- Protection contre path traversal
- Gestion propre des signaux (SIGINT, SIGTERM)
- Support SELinux et conteneurs rootless

## 🏥 Contexte Médical

Ce testeur est conçu pour valider la communication avec des automates de biologie médicale utilisant :
- **Protocoles** : HL7, POCT1-A
- **Transport** : TCP/IP
- **Messages** : Binaires avec accusés de réception

## 📋 Prérequis

- **OS** : Red Hat Enterprise Linux 9+ (recommandé)
- **Compilateur** : GCC avec support C11
- **Outils** : CMake 3.10+, Make, Podman
- **Architecture** : x86_64, ARM64 (iMX8 mini)

## 🤝 Contribution

1. Consulter `IMPLEMENTATION.md` pour les détails techniques
2. Utiliser `scripts/dev.sh` pour le développement
3. Exécuter les tests avant commit : `tests/test_suite.sh`

## 📄 Licence

Projet propriétaire - Usage interne uniquement

---

**Version** : 2.0.0  
**Dernière mise à jour** : Septembre 2025  
**Plateforme cible** : Red Hat Enterprise Linux + iMX8 mini
